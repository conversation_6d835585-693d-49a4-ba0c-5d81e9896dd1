import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/common/util';
import { Brand } from 'app/core/models/brand-model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Subject } from 'rxjs';
import { ReplaySubject, takeUntil } from 'rxjs';

import { BRAND_FORM_MODEL } from './brand-form.model';

@Component({
  selector: 'tps-create-edit-brand',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-brand.component.html',
})
export class CreateEditBrandComponent implements OnInit, OnD<PERSON>roy {
  public hdr: string = 'Create Brand';
  public brandForm: FormGroup;
  public fields: any[] = [];
  public rec: Brand = new Brand();
  isMobile: boolean = false;

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;

  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);

  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.status = this._dialogConfig.data?.status;
    this.buildForm();
    this.configureForm();
  }

  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(BRAND_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(BRAND_FORM_MODEL);
    this.brandForm = new FormGroup(formGroupFields);
  }

  //configure form
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Brand";
        this.setDataToForm();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = "View Brand";
        this.setDataToForm();
        this.brandForm.disable();
        break;
      default:
        break;
    }
  }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    this.brandForm = this._formFactoryService.setFormControlsValues(this.brandForm, this.rec, this.fields);
  }

  //submit form
  public onSubmit() {
    if (this.isMobile) {
      // Add any mobile-specific handling here
    }
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.brandForm, this.rec, this.fields);

    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update a brand`, this.rec.name)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            APP_UI_CONFIG.master.brand.update.paramList.brandUid = this.rec.uuid;
            this._invokeService.serviceInvocation(APP_UI_CONFIG.master.brand.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Brand updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the brand details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    } else {
      this._commonService.confirm(`Are you sure you want to create a brand`, this.rec.name)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.master.brand.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New brand created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the brand details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    }
  }

  //close modal
  public close(status) {
    this._dialogConfigRef.close(status);
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}